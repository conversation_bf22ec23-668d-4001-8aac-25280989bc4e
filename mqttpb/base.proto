syntax = "proto3";
package rrpc.base.v1;

option go_package = "mqttpb/";

import "google/protobuf/descriptor.proto";
import "google/protobuf/any.proto";

enum ContentEncoding {
	PLAIN = 0;
	GZIP = 1;
	DEFLATE = 2;
	BROTLI = 3;
}

message Request {
	uint64 id = 1;
	string method = 2;
	ContentEncoding encoding = 3;
	map<string, string> metadata = 5;
	google.protobuf.Any body = 4;
}

message Response {
	uint64 id = 1;
	int32 status = 2;
	string error_message = 3;
	ContentEncoding encoding = 5;
	map<string, string> metadata = 7;
	google.protobuf.Any body = 6;
}
