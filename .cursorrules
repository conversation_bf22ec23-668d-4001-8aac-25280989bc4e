1. Response Format
- Must use English
- Present an overview of the changes you will do
- Do not generate any code until I ask you to proceed
- Provide direct code solutions instead of high-level descriptions
- Show only relevant code changes with minimal context
- Split long responses into multiple messages if needed

2. Code Standards
- Follow SOLID principles (especially Open-Closed and Interface Segregation)
- Implement proper error handling
- Add comments only for complex logic
- Focus on performance optimization
- Never delete existing code comments
- Always insert new line at the end of the file

3. Project Management
- Document key decisions and changes in README.md
