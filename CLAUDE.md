# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Test
- `make test` - Run all tests
- `make cover` - Run tests with coverage report
- `make cover-report` - Generate HTML coverage report
- `go test ./...` - Run all tests (alternative)

### Code Generation
- `make generate` - Generate all Go code (mocks, etc.)
- `make mocks` - Generate mock implementations
- `make protoc` - Generate protobuf Go files from .proto definitions

### Code Quality
- `make lint` - Run linter (golint)
- `make deps` - Update Go module dependencies

### Examples
- `go run examples/basic/server/main.go` - Start example server
- `go run examples/basic/client/main.go` - Run example client (requires server)

## Architecture Overview

This is a Go reverse RPC framework that inverts the traditional client-server model. Instead of clients connecting to servers, both connect to a message broker (typically MQTT):

```
Traditional: Client → Server
Reverse RPC: Client → Broker ← Server
```

### Key Components

1. **Transport Adapters** (`mqttadapter/`, `mqttjson/`, `mqttpb/`)
   - Abstract different messaging protocols
   - Currently supports MQTT with JSON and Protobuf encoding
   - Each adapter has separate packages for encoding formats

2. **Core RPC Framework** (`server.go`, `context.go`)
   - `Server` handles incoming requests and manages handlers
   - Uses worker pools and rate limiting for performance
   - Supports method registration and request routing

3. **Telemetry** (`telemetry/`)
   - Integrated OpenTelemetry support for tracing and metrics
   - Configurable debug/production modes
   - Tracks request duration, error counts, and custom metrics

4. **Compression** (`compressor/`)
   - Pluggable payload compression
   - Supports various algorithms (Gzip, Brotli, etc.)

### Directory Structure
- `mqttadapter/` - MQTT transport adapter interface and implementation
- `mqttjson/` - MQTT transport with JSON encoding
- `mqttpb/` - MQTT transport with Protobuf encoding
- `telemetry/` - OpenTelemetry integration
- `compressor/` - Payload compression utilities
- `examples/` - Working examples and demos
- `mock/` - Generated mock implementations for testing

## Testing

- Test files use `*_test.go` naming convention
- E2E tests available in adapter packages (`e2e_test.go`)
- Mock implementations generated with `go generate`
- Run specific package tests: `go test ./mqttjson`

## Configuration

Server configuration uses functional options pattern:
- `WithServerName()` - Set server name for metrics
- `WithLogResponse()` - Enable response logging
- `WithLimiter()` - Configure rate limiting
- `WithWorkerNum()` - Set worker pool size

## Key Files

- `server.go` - Main RPC server implementation
- `context.go` - Request context interface
- `constants.go` - RPC status codes and constants
- `telemetry/telemetry.go` - OpenTelemetry setup and instrumentation
- `docs/ARCHITECTURE.md` - Detailed architecture documentation

## Development Notes

- Follow SOLID principles, especially Open-Closed and Interface Segregation
- Use structured error handling with `cockroachdb/errors`
- Implement proper timeouts and rate limiting
- Focus on performance optimization
- OpenTelemetry is preferred over deprecated Prometheus metrics
- All new code should include telemetry instrumentation