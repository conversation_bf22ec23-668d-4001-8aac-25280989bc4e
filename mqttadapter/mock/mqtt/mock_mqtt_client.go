// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/eclipse/paho.mqtt.golang (interfaces: Client,Token,Message)
//
// Generated by this command:
//
//	mockgen -package mock_mqtt -destination=mock/mqtt/mock_mqtt_client.go github.com/eclipse/paho.mqtt.golang Client,Token,Message
//

// Package mock_mqtt is a generated GoMock package.
package mock_mqtt

import (
	reflect "reflect"
	time "time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// AddRoute mocks base method.
func (m *MockClient) AddRoute(arg0 string, arg1 mqtt.MessageHandler) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddRoute", arg0, arg1)
}

// AddRoute indicates an expected call of AddRoute.
func (mr *MockClientMockRecorder) AddRoute(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRoute", reflect.TypeOf((*MockClient)(nil).AddRoute), arg0, arg1)
}

// Connect mocks base method.
func (m *MockClient) Connect() mqtt.Token {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Connect")
	ret0, _ := ret[0].(mqtt.Token)
	return ret0
}

// Connect indicates an expected call of Connect.
func (mr *MockClientMockRecorder) Connect() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Connect", reflect.TypeOf((*MockClient)(nil).Connect))
}

// Disconnect mocks base method.
func (m *MockClient) Disconnect(arg0 uint) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Disconnect", arg0)
}

// Disconnect indicates an expected call of Disconnect.
func (mr *MockClientMockRecorder) Disconnect(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Disconnect", reflect.TypeOf((*MockClient)(nil).Disconnect), arg0)
}

// IsConnected mocks base method.
func (m *MockClient) IsConnected() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsConnected")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsConnected indicates an expected call of IsConnected.
func (mr *MockClientMockRecorder) IsConnected() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsConnected", reflect.TypeOf((*MockClient)(nil).IsConnected))
}

// IsConnectionOpen mocks base method.
func (m *MockClient) IsConnectionOpen() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsConnectionOpen")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsConnectionOpen indicates an expected call of IsConnectionOpen.
func (mr *MockClientMockRecorder) IsConnectionOpen() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsConnectionOpen", reflect.TypeOf((*MockClient)(nil).IsConnectionOpen))
}

// OptionsReader mocks base method.
func (m *MockClient) OptionsReader() mqtt.ClientOptionsReader {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OptionsReader")
	ret0, _ := ret[0].(mqtt.ClientOptionsReader)
	return ret0
}

// OptionsReader indicates an expected call of OptionsReader.
func (mr *MockClientMockRecorder) OptionsReader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OptionsReader", reflect.TypeOf((*MockClient)(nil).OptionsReader))
}

// Publish mocks base method.
func (m *MockClient) Publish(arg0 string, arg1 byte, arg2 bool, arg3 any) mqtt.Token {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(mqtt.Token)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockClientMockRecorder) Publish(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockClient)(nil).Publish), arg0, arg1, arg2, arg3)
}

// Subscribe mocks base method.
func (m *MockClient) Subscribe(arg0 string, arg1 byte, arg2 mqtt.MessageHandler) mqtt.Token {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", arg0, arg1, arg2)
	ret0, _ := ret[0].(mqtt.Token)
	return ret0
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockClientMockRecorder) Subscribe(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockClient)(nil).Subscribe), arg0, arg1, arg2)
}

// SubscribeMultiple mocks base method.
func (m *MockClient) SubscribeMultiple(arg0 map[string]byte, arg1 mqtt.MessageHandler) mqtt.Token {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeMultiple", arg0, arg1)
	ret0, _ := ret[0].(mqtt.Token)
	return ret0
}

// SubscribeMultiple indicates an expected call of SubscribeMultiple.
func (mr *MockClientMockRecorder) SubscribeMultiple(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeMultiple", reflect.TypeOf((*MockClient)(nil).SubscribeMultiple), arg0, arg1)
}

// Unsubscribe mocks base method.
func (m *MockClient) Unsubscribe(arg0 ...string) mqtt.Token {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Unsubscribe", varargs...)
	ret0, _ := ret[0].(mqtt.Token)
	return ret0
}

// Unsubscribe indicates an expected call of Unsubscribe.
func (mr *MockClientMockRecorder) Unsubscribe(arg0 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unsubscribe", reflect.TypeOf((*MockClient)(nil).Unsubscribe), arg0...)
}

// MockToken is a mock of Token interface.
type MockToken struct {
	ctrl     *gomock.Controller
	recorder *MockTokenMockRecorder
}

// MockTokenMockRecorder is the mock recorder for MockToken.
type MockTokenMockRecorder struct {
	mock *MockToken
}

// NewMockToken creates a new mock instance.
func NewMockToken(ctrl *gomock.Controller) *MockToken {
	mock := &MockToken{ctrl: ctrl}
	mock.recorder = &MockTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockToken) EXPECT() *MockTokenMockRecorder {
	return m.recorder
}

// Done mocks base method.
func (m *MockToken) Done() <-chan struct{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Done")
	ret0, _ := ret[0].(<-chan struct{})
	return ret0
}

// Done indicates an expected call of Done.
func (mr *MockTokenMockRecorder) Done() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Done", reflect.TypeOf((*MockToken)(nil).Done))
}

// Error mocks base method.
func (m *MockToken) Error() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Error")
	ret0, _ := ret[0].(error)
	return ret0
}

// Error indicates an expected call of Error.
func (mr *MockTokenMockRecorder) Error() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Error", reflect.TypeOf((*MockToken)(nil).Error))
}

// Wait mocks base method.
func (m *MockToken) Wait() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Wait")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Wait indicates an expected call of Wait.
func (mr *MockTokenMockRecorder) Wait() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Wait", reflect.TypeOf((*MockToken)(nil).Wait))
}

// WaitTimeout mocks base method.
func (m *MockToken) WaitTimeout(arg0 time.Duration) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitTimeout", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// WaitTimeout indicates an expected call of WaitTimeout.
func (mr *MockTokenMockRecorder) WaitTimeout(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitTimeout", reflect.TypeOf((*MockToken)(nil).WaitTimeout), arg0)
}

// MockMessage is a mock of Message interface.
type MockMessage struct {
	ctrl     *gomock.Controller
	recorder *MockMessageMockRecorder
}

// MockMessageMockRecorder is the mock recorder for MockMessage.
type MockMessageMockRecorder struct {
	mock *MockMessage
}

// NewMockMessage creates a new mock instance.
func NewMockMessage(ctrl *gomock.Controller) *MockMessage {
	mock := &MockMessage{ctrl: ctrl}
	mock.recorder = &MockMessageMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMessage) EXPECT() *MockMessageMockRecorder {
	return m.recorder
}

// Ack mocks base method.
func (m *MockMessage) Ack() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Ack")
}

// Ack indicates an expected call of Ack.
func (mr *MockMessageMockRecorder) Ack() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ack", reflect.TypeOf((*MockMessage)(nil).Ack))
}

// Duplicate mocks base method.
func (m *MockMessage) Duplicate() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Duplicate")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Duplicate indicates an expected call of Duplicate.
func (mr *MockMessageMockRecorder) Duplicate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Duplicate", reflect.TypeOf((*MockMessage)(nil).Duplicate))
}

// MessageID mocks base method.
func (m *MockMessage) MessageID() uint16 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MessageID")
	ret0, _ := ret[0].(uint16)
	return ret0
}

// MessageID indicates an expected call of MessageID.
func (mr *MockMessageMockRecorder) MessageID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MessageID", reflect.TypeOf((*MockMessage)(nil).MessageID))
}

// Payload mocks base method.
func (m *MockMessage) Payload() []byte {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Payload")
	ret0, _ := ret[0].([]byte)
	return ret0
}

// Payload indicates an expected call of Payload.
func (mr *MockMessageMockRecorder) Payload() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Payload", reflect.TypeOf((*MockMessage)(nil).Payload))
}

// Qos mocks base method.
func (m *MockMessage) Qos() byte {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Qos")
	ret0, _ := ret[0].(byte)
	return ret0
}

// Qos indicates an expected call of Qos.
func (mr *MockMessageMockRecorder) Qos() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Qos", reflect.TypeOf((*MockMessage)(nil).Qos))
}

// Retained mocks base method.
func (m *MockMessage) Retained() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Retained")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Retained indicates an expected call of Retained.
func (mr *MockMessageMockRecorder) Retained() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Retained", reflect.TypeOf((*MockMessage)(nil).Retained))
}

// Topic mocks base method.
func (m *MockMessage) Topic() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Topic")
	ret0, _ := ret[0].(string)
	return ret0
}

// Topic indicates an expected call of Topic.
func (mr *MockMessageMockRecorder) Topic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Topic", reflect.TypeOf((*MockMessage)(nil).Topic))
}
