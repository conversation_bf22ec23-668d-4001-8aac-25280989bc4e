// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -source=interface.go -destination=mock/mock_mqttadapter.go
//

// Package mock_mqttadapter is a generated GoMock package.
package mock_mqttadapter

import (
	context "context"
	reflect "reflect"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	mqttadapter "github.com/xizhibei/go-reverse-rpc/mqttadapter"
	gomock "go.uber.org/mock/gomock"
)

// MockMQTTClientAdapter is a mock of MQTTClientAdapter interface.
type MockMQTTClientAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockMQTTClientAdapterMockRecorder
}

// MockMQTTClientAdapterMockRecorder is the mock recorder for MockMQTTClientAdapter.
type MockMQTTClientAdapterMockRecorder struct {
	mock *MockMQTTClientAdapter
}

// NewMockMQTTClientAdapter creates a new mock instance.
func NewMockMQTTClientAdapter(ctrl *gomock.Controller) *MockMQTTClientAdapter {
	mock := &MockMQTTClientAdapter{ctrl: ctrl}
	mock.recorder = &MockMQTTClientAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMQTTClientAdapter) EXPECT() *MockMQTTClientAdapterMockRecorder {
	return m.recorder
}

// Connect mocks base method.
func (m *MockMQTTClientAdapter) Connect(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Connect", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Connect indicates an expected call of Connect.
func (mr *MockMQTTClientAdapterMockRecorder) Connect(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Connect", reflect.TypeOf((*MockMQTTClientAdapter)(nil).Connect), ctx)
}

// ConnectAndWaitForSuccess mocks base method.
func (m *MockMQTTClientAdapter) ConnectAndWaitForSuccess() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ConnectAndWaitForSuccess")
}

// ConnectAndWaitForSuccess indicates an expected call of ConnectAndWaitForSuccess.
func (mr *MockMQTTClientAdapterMockRecorder) ConnectAndWaitForSuccess() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConnectAndWaitForSuccess", reflect.TypeOf((*MockMQTTClientAdapter)(nil).ConnectAndWaitForSuccess))
}

// Disconnect mocks base method.
func (m *MockMQTTClientAdapter) Disconnect() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Disconnect")
}

// Disconnect indicates an expected call of Disconnect.
func (mr *MockMQTTClientAdapterMockRecorder) Disconnect() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Disconnect", reflect.TypeOf((*MockMQTTClientAdapter)(nil).Disconnect))
}

// EnsureConnected mocks base method.
func (m *MockMQTTClientAdapter) EnsureConnected() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnsureConnected")
}

// EnsureConnected indicates an expected call of EnsureConnected.
func (mr *MockMQTTClientAdapterMockRecorder) EnsureConnected() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureConnected", reflect.TypeOf((*MockMQTTClientAdapter)(nil).EnsureConnected))
}

// GetClientOptions mocks base method.
func (m *MockMQTTClientAdapter) GetClientOptions() *mqtt.ClientOptions {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientOptions")
	ret0, _ := ret[0].(*mqtt.ClientOptions)
	return ret0
}

// GetClientOptions indicates an expected call of GetClientOptions.
func (mr *MockMQTTClientAdapterMockRecorder) GetClientOptions() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientOptions", reflect.TypeOf((*MockMQTTClientAdapter)(nil).GetClientOptions))
}

// GetMqttClient mocks base method.
func (m *MockMQTTClientAdapter) GetMqttClient() mqtt.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMqttClient")
	ret0, _ := ret[0].(mqtt.Client)
	return ret0
}

// GetMqttClient indicates an expected call of GetMqttClient.
func (mr *MockMQTTClientAdapterMockRecorder) GetMqttClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMqttClient", reflect.TypeOf((*MockMQTTClientAdapter)(nil).GetMqttClient))
}

// IsConnected mocks base method.
func (m *MockMQTTClientAdapter) IsConnected() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsConnected")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsConnected indicates an expected call of IsConnected.
func (mr *MockMQTTClientAdapterMockRecorder) IsConnected() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsConnected", reflect.TypeOf((*MockMQTTClientAdapter)(nil).IsConnected))
}

// OffConnect mocks base method.
func (m *MockMQTTClientAdapter) OffConnect(idx int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OffConnect", idx)
}

// OffConnect indicates an expected call of OffConnect.
func (mr *MockMQTTClientAdapterMockRecorder) OffConnect(idx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OffConnect", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OffConnect), idx)
}

// OffConnectLost mocks base method.
func (m *MockMQTTClientAdapter) OffConnectLost(idx int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OffConnectLost", idx)
}

// OffConnectLost indicates an expected call of OffConnectLost.
func (mr *MockMQTTClientAdapterMockRecorder) OffConnectLost(idx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OffConnectLost", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OffConnectLost), idx)
}

// OnConnect mocks base method.
func (m *MockMQTTClientAdapter) OnConnect(cb mqttadapter.OnConnectCallback) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnConnect", cb)
	ret0, _ := ret[0].(int)
	return ret0
}

// OnConnect indicates an expected call of OnConnect.
func (mr *MockMQTTClientAdapterMockRecorder) OnConnect(cb any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnConnect", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OnConnect), cb)
}

// OnConnectLost mocks base method.
func (m *MockMQTTClientAdapter) OnConnectLost(cb mqttadapter.OnConnectLostCallback) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnConnectLost", cb)
	ret0, _ := ret[0].(int)
	return ret0
}

// OnConnectLost indicates an expected call of OnConnectLost.
func (mr *MockMQTTClientAdapterMockRecorder) OnConnectLost(cb any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnConnectLost", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OnConnectLost), cb)
}

// OnConnectLostOnce mocks base method.
func (m *MockMQTTClientAdapter) OnConnectLostOnce(cb mqttadapter.OnConnectLostCallback) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OnConnectLostOnce", cb)
}

// OnConnectLostOnce indicates an expected call of OnConnectLostOnce.
func (mr *MockMQTTClientAdapterMockRecorder) OnConnectLostOnce(cb any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnConnectLostOnce", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OnConnectLostOnce), cb)
}

// OnConnectOnce mocks base method.
func (m *MockMQTTClientAdapter) OnConnectOnce(cb mqttadapter.OnConnectCallback) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OnConnectOnce", cb)
}

// OnConnectOnce indicates an expected call of OnConnectOnce.
func (mr *MockMQTTClientAdapterMockRecorder) OnConnectOnce(cb any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnConnectOnce", reflect.TypeOf((*MockMQTTClientAdapter)(nil).OnConnectOnce), cb)
}

// PublishBytes mocks base method.
func (m *MockMQTTClientAdapter) PublishBytes(ctx context.Context, topic string, qos byte, retained bool, data []byte) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishBytes", ctx, topic, qos, retained, data)
}

// PublishBytes indicates an expected call of PublishBytes.
func (mr *MockMQTTClientAdapterMockRecorder) PublishBytes(ctx, topic, qos, retained, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishBytes", reflect.TypeOf((*MockMQTTClientAdapter)(nil).PublishBytes), ctx, topic, qos, retained, data)
}

// PublishBytesWait mocks base method.
func (m *MockMQTTClientAdapter) PublishBytesWait(ctx context.Context, topic string, qos byte, retained bool, data []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishBytesWait", ctx, topic, qos, retained, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishBytesWait indicates an expected call of PublishBytesWait.
func (mr *MockMQTTClientAdapterMockRecorder) PublishBytesWait(ctx, topic, qos, retained, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishBytesWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).PublishBytesWait), ctx, topic, qos, retained, data)
}

// PublishObject mocks base method.
func (m *MockMQTTClientAdapter) PublishObject(ctx context.Context, topic string, qos byte, retained bool, payload any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishObject", ctx, topic, qos, retained, payload)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishObject indicates an expected call of PublishObject.
func (mr *MockMQTTClientAdapterMockRecorder) PublishObject(ctx, topic, qos, retained, payload any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishObject", reflect.TypeOf((*MockMQTTClientAdapter)(nil).PublishObject), ctx, topic, qos, retained, payload)
}

// PublishObjectWait mocks base method.
func (m *MockMQTTClientAdapter) PublishObjectWait(ctx context.Context, topic string, qos byte, retained bool, payload any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishObjectWait", ctx, topic, qos, retained, payload)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishObjectWait indicates an expected call of PublishObjectWait.
func (mr *MockMQTTClientAdapterMockRecorder) PublishObjectWait(ctx, topic, qos, retained, payload any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishObjectWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).PublishObjectWait), ctx, topic, qos, retained, payload)
}

// Subscribe mocks base method.
func (m *MockMQTTClientAdapter) Subscribe(ctx context.Context, topic string, qos byte, onMsg mqttadapter.MessageCallback) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Subscribe", ctx, topic, qos, onMsg)
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockMQTTClientAdapterMockRecorder) Subscribe(ctx, topic, qos, onMsg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockMQTTClientAdapter)(nil).Subscribe), ctx, topic, qos, onMsg)
}

// SubscribeMultiple mocks base method.
func (m *MockMQTTClientAdapter) SubscribeMultiple(ctx context.Context, filters map[string]byte, onMsg mqttadapter.MessageCallback) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SubscribeMultiple", ctx, filters, onMsg)
}

// SubscribeMultiple indicates an expected call of SubscribeMultiple.
func (mr *MockMQTTClientAdapterMockRecorder) SubscribeMultiple(ctx, filters, onMsg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeMultiple", reflect.TypeOf((*MockMQTTClientAdapter)(nil).SubscribeMultiple), ctx, filters, onMsg)
}

// SubscribeMultipleWait mocks base method.
func (m *MockMQTTClientAdapter) SubscribeMultipleWait(ctx context.Context, filters map[string]byte, onMsg mqttadapter.MessageCallback) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeMultipleWait", ctx, filters, onMsg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubscribeMultipleWait indicates an expected call of SubscribeMultipleWait.
func (mr *MockMQTTClientAdapterMockRecorder) SubscribeMultipleWait(ctx, filters, onMsg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeMultipleWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).SubscribeMultipleWait), ctx, filters, onMsg)
}

// SubscribeWait mocks base method.
func (m *MockMQTTClientAdapter) SubscribeWait(ctx context.Context, topic string, qos byte, onMsg mqttadapter.MessageCallback) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeWait", ctx, topic, qos, onMsg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubscribeWait indicates an expected call of SubscribeWait.
func (mr *MockMQTTClientAdapterMockRecorder) SubscribeWait(ctx, topic, qos, onMsg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).SubscribeWait), ctx, topic, qos, onMsg)
}

// Unsubscribe mocks base method.
func (m *MockMQTTClientAdapter) Unsubscribe(ctx context.Context, topic string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Unsubscribe", ctx, topic)
}

// Unsubscribe indicates an expected call of Unsubscribe.
func (mr *MockMQTTClientAdapterMockRecorder) Unsubscribe(ctx, topic any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unsubscribe", reflect.TypeOf((*MockMQTTClientAdapter)(nil).Unsubscribe), ctx, topic)
}

// UnsubscribeAll mocks base method.
func (m *MockMQTTClientAdapter) UnsubscribeAll(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UnsubscribeAll", ctx)
}

// UnsubscribeAll indicates an expected call of UnsubscribeAll.
func (mr *MockMQTTClientAdapterMockRecorder) UnsubscribeAll(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnsubscribeAll", reflect.TypeOf((*MockMQTTClientAdapter)(nil).UnsubscribeAll), ctx)
}

// UnsubscribeAllWait mocks base method.
func (m *MockMQTTClientAdapter) UnsubscribeAllWait(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnsubscribeAllWait", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnsubscribeAllWait indicates an expected call of UnsubscribeAllWait.
func (mr *MockMQTTClientAdapterMockRecorder) UnsubscribeAllWait(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnsubscribeAllWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).UnsubscribeAllWait), ctx)
}

// UnsubscribeWait mocks base method.
func (m *MockMQTTClientAdapter) UnsubscribeWait(ctx context.Context, topic string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnsubscribeWait", ctx, topic)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnsubscribeWait indicates an expected call of UnsubscribeWait.
func (mr *MockMQTTClientAdapterMockRecorder) UnsubscribeWait(ctx, topic any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnsubscribeWait", reflect.TypeOf((*MockMQTTClientAdapter)(nil).UnsubscribeWait), ctx, topic)
}
