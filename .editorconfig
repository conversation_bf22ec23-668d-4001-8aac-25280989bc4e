# EditorConfig is awesome: http://EditorConfig.org

# For vim: https://github.com/editorconfig/editorconfig-vim#readme

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# Tab indentation (no size specified)
[Makefile]
indent_style = tab

[**.go]
indent_style = tab
indent_size = 2

[**.yaml]
indent_style = space
indent_size = 2

[**.proto]
indent_style = tab
indent_size = 2
