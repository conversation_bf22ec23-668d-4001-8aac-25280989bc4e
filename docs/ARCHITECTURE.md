# Go Reverse RPC Architecture

## 1. Introduction

`go-reverse-rpc` is a Go library that facilitates remote procedure calls (RPC) where the typical client-server relationship is inverted. In this model, the "server" (the entity exposing services) initiates a connection to a message broker or a central "client" endpoint, and the "client" (the entity consuming services) sends requests and receives responses over this established reverse connection.

```mermaid
graph LR
    subgraph "Traditional RPC"
        C1[Client] -->|connects to| S1[Server]
    end

    subgraph "Reverse RPC"
        S2[Server] -->|connects to| B[Message Broker]
        C2[Client] -->|connects to| B
        C2 -->|sends request| B
        B -->|forwards request| S2
        S2 -->|sends response| B
        B -->|forwards response| C2
    end
```

This architecture is particularly useful in scenarios where:

*   Services are deployed behind restrictive firewalls or NATs, making it difficult for them to accept incoming connections directly.
*   Services need to dynamically register and deregister themselves.
*   A central point of control or routing for RPC calls is desired.

The primary goal of `go-reverse-rpc` is to provide a flexible, extensible, and observable framework for building such reverse RPC systems in Go.

## 2. Core Concepts

*   **Reverse Connection:** The service provider (server-side logic) connects outbound to a message broker or a client-managed endpoint, rather than listening for inbound connections.
*   **Client:** The application that invokes RPC methods on the server. It sends requests through a transport adapter and receives responses.
*   **Server:** The application that implements the RPC methods. It listens for requests via a transport adapter over its outbound-established connection and sends back responses.
*   **Adapters:** Pluggable components responsible for communication over specific messaging protocols (e.g., MQTT, WebSockets, AMQP). They handle the specifics of message sending, receiving, and topic/queue management.
*   **Encoders/Decoders:** Components responsible for serializing and deserializing RPC requests and responses into various data formats (e.g., Protobuf, JSON).
*   **Message Broker (Optional but Common):** Often, a message broker (like MQTT or AMQP broker) sits between the client and server, facilitating asynchronous communication and decoupling. The server connects to the broker, and the client sends/receives messages via the broker.

## 3. Key Components

The `go-reverse-rpc` system is composed of several key components:

```mermaid
graph TB
    subgraph "RPC Client"
        C[Client Application]
        CA[Client Adapter]
        CE[Client Encoder/Decoder]
        CT[Client Telemetry]
    end

    subgraph "Message Broker"
        B[MQTT/WebSocket/AMQP Broker]
    end

    subgraph "RPC Server"
        S[Server Application]
        SA[Server Adapter]
        SE[Server Encoder/Decoder]
        ST[Server Telemetry]
    end

    C --> CA
    CA --> CE
    CE --> B
    B --> SE
    SE --> SA
    SA --> S

    CT -.-> C
    CT -.-> CA
    ST -.-> S
    ST -.-> SA
```

### 3.1. RPC Client

*   Responsible for initiating RPC calls.
*   Uses a specific transport adapter to send requests and receive responses.
*   Handles encoding of requests and decoding of responses.
*   Manages call timeouts and potentially retry logic.
*   Planned features include client-side load balancing and failover.

### 3.2. RPC Server

*   Responsible for implementing the service methods.
*   Uses a specific transport adapter to listen for incoming requests and send responses.
*   Handles decoding of requests and encoding of responses.
*   Registers service handlers that map RPC method names to actual Go functions.
*   Planned features include server-side streaming RPCs.

### 3.3. Transport Adapters

Adapters are crucial for abstracting the underlying communication protocol. They provide a consistent interface for the client and server components.

```mermaid
graph LR
    subgraph "Transport Adapters"
        M[MQTT Adapter]
        W[WebSocket Adapter]
        A[AMQP Adapter]
        G[gRPC Adapter]
    end

    subgraph "Encoding"
        P[Protobuf]
        J[JSON]
        MP[MessagePack]
        AV[Avro]
    end

    M --> P
    M --> J
    W --> P
    W --> J
    A --> P
    A --> J
    G --> P
```

*   **`mqttadapter`:** (Existing) Supports MQTT (v3.1.1, with planned v5 support). Handles connecting to an MQTT broker, subscribing to request topics, and publishing responses.
*   **`websocketadapter`:** (Planned) Will support RPC over WebSockets.
*   **`amqpadapter`:** (Planned) Will support RPC over AMQP.
*   **`grpcadapter`:** (Planned) Will explore using gRPC as a transport, leveraging its features.

Each adapter typically has corresponding `pb` (Protobuf) and `json` (JSON) packages for encoding/decoding specific to the transport's message structure (e.g., `mqttpb`, `mqttjson`).

### 3.4. Encoding and Data Handling

`go-reverse-rpc` aims to be flexible in terms of data serialization formats.

*   **Protobuf (`pb`):** Commonly used for efficient binary serialization.
*   **JSON (`json`):** Human-readable text-based format.
*   **MessagePack:** (Planned) A fast and compact binary serialization format.
*   **Avro:** (Planned) Another popular binary serialization format.

Schema management, potentially via integration with a schema registry, is a consideration for future enhancements.

### 3.5. Compression

*   A `compressor` package exists to reduce payload size.
*   Effectiveness and choice of algorithms (Gzip, Brotli, Snappy, Zstd) are subject to review and benchmarking.
*   Configurable compression levels are planned.

### 3.6. Telemetry and Observability

*   Integrated with **OpenTelemetry** (`go.opentelemetry.io/otel`).
*   Aims for comprehensive tracing and metrics for all critical paths (client calls, server handling, adapter interactions, encoding/decoding, compression).
*   Focus on trace context propagation.
*   Enhanced metrics are planned, including payload sizes, error rates by type, queue lengths, and connection status.

## 4. Request Flow (Illustrative Example with MQTT)

```mermaid
sequenceDiagram
    participant C as Client
    participant CA as Client Adapter
    participant B as MQTT Broker
    participant SA as Server Adapter
    participant S as Server

    Note over S,B: Initial Setup
    S->>SA: Initialize
    SA->>B: Connect & Subscribe
    C->>CA: Initialize
    CA->>B: Connect

    Note over C,S: RPC Call Flow
    C->>CA: Call MyService.MyMethod
    CA->>CA: Encode Request
    CA->>B: Publish Request
    B->>SA: Forward Request
    SA->>SA: Decode Request
    SA->>S: Invoke Handler
    S->>SA: Return Response
    SA->>SA: Encode Response
    SA->>B: Publish Response
    B->>CA: Forward Response
    CA->>CA: Decode Response
    CA->>C: Return Result
```

1.  **Setup:**
    *   The RPC Server starts, initializes its `mqttadapter`, and connects to an MQTT broker. It subscribes to a specific topic (e.g., `service/my_service/requests`).
    *   The RPC Client starts, initializes its `mqttadapter`, and connects to the same MQTT broker.

2.  **RPC Call:**
    *   The Client application calls a service method (e.g., `MyService.MyMethod(request)`).
    *   The Client's RPC framework, using the configured encoder (e.g., Protobuf), serializes the `request`.
    *   The `mqttadapter` on the client side publishes this serialized request as a message to the topic the server is subscribed to (e.g., `service/my_service/requests`). The message might include a reply-to topic.
    *   The message might be compressed before sending.

3.  **Server Processing:**
    *   The MQTT broker routes the message to the RPC Server.
    *   The Server's `mqttadapter` receives the message.
    *   The message might be decompressed.
    *   The Server's RPC framework decodes the message using the corresponding decoder.
    *   The framework identifies the target service and method (`MyService.MyMethod`) and invokes the registered Go function with the deserialized request.

4.  **Response:**
    *   The server-side Go function executes and returns a response (or an error).
    *   The Server's RPC framework serializes the response (or error) using the encoder.
    *   The message might be compressed.
    *   The Server's `mqttadapter` publishes the serialized response back, typically to a reply-to topic specified by the client or a predefined response topic.

5.  **Client Receives Response:**
    *   The Client's `mqttadapter` receives the response message.
    *   The message might be decompressed.
    *   The Client's RPC framework decodes the response.
    *   The original calling code in the Client application receives the deserialized response (or error).

## 5. Extensibility

The architecture is designed for extensibility:

*   **New Protocols:** Implemented by creating a new adapter that conforms to the required interfaces for sending/receiving messages and managing connections.
*   **New Encoding Formats:** Implemented by creating new encoder/decoder packages that satisfy the serialization/deserialization interfaces.

## 6. Cross-Cutting Concerns

```mermaid
graph TB
    subgraph "Cross-Cutting Concerns"
        E[Error Handling]
        C[Configuration]
        A[Auth/Authz]
        T[Telemetry]
    end

    E --> E1[Structured Errors]
    E --> E2[Error Propagation]
    E --> E3[DLQ]

    C --> C1[Server Options]
    C --> C2[Adapter Options]
    C --> C3[Env Vars]

    A --> A1[Transport Auth]
    A --> A2[Custom Auth]
    A --> A3[Middleware]

    T --> T1[Traces]
    T --> T2[Metrics]
    T --> T3[Logs]
```

### 6.1. Error Handling

*   Utilizes the `cockroachdb/errors` library for structured error handling and propagation.
*   Aims for typed errors that carry sufficient context across client, server, and adapters.
*   Planned: Dead Letter Queue (DLQ) for failed requests.

### 6.2. Configuration

*   Server options (`server_option.go`) and adapter options (e.g., `mqttadapter/options.go`) provide configuration points.
*   Aims for a consistent and clear configuration approach, potentially using a library like Viper for struct-based configuration and environment variable overrides.

### 6.3. Authentication and Authorization

*   Currently, authentication may be tied to the underlying transport (e.g., MQTT username/password).
*   Planned: Pluggable authentication/authorization mechanisms at the RPC layer, allowing custom logic via hooks or middleware.

## 7. Future Architectural Considerations

The roadmap includes several enhancements that will impact the architecture:

*   **Server-Side Streaming RPCs:** Requires changes to how servers handle requests and send multiple responses.
*   **Client-Side Load Balancing/Failover:** Introduces logic for managing multiple server instances and routing requests.
*   **Pluggable Authentication/Authorization:** Will define clear interfaces for security extensions.
*   **Schema Registry Integration:** For advanced schema management with Protobuf/Avro.

## 8. Documentation and Developer Experience

*   Emphasis on GoDoc for API documentation.
*   Provision of comprehensive examples.
*   Development of a benchmarking suite.
*   Focus on high code coverage.

This document provides a high-level overview of the `go-reverse-rpc` architecture. As the project evolves, this document will be updated to reflect new components, design decisions, and enhancements.
