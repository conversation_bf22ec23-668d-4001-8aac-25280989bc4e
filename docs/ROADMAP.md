# Go Reverse RPC Roadmap

This document outlines the planned features, enhancements, and tasks for the `go-reverse-rpc` project.

## I. Protocol Enhancements (Expanding Connectivity)

*   **MQTT v5 Protocol Support:**
    *   Integrate a Go MQTT v5 client library.
    *   Update `mqttadapter` to support MQTT v5 features (e.g., user properties, request/response pattern, shared subscriptions, message expiry).
    *   Add examples and tests for MQTT v5.
*   **WebSocket Protocol Support:**
    *   Implement a WebSocket adapter similar to `mqttadapter`.
    *   Choose a suitable Go WebSocket library (e.g., `gorilla/websocket`).
    *   Develop `websocketpb` and `websocketjson` packages for Protobuf and JSON encoding over WebSockets.
    *   Add examples and tests.
*   **AMQP Protocol Support:**
    *   Research and select a Go AMQP 1.0 client library.
    *   Implement an AMQP adapter.
    *   Develop `amqppb` and `amqpjson` (or similar) packages.
    *   Add examples and tests.
*   **(New) gRPC Transport Support:**
    *   Explore using gRPC as a transport layer. This would allow leveraging gRPC's ecosystem and features like bidirectional streaming more natively.
    *   This might involve creating a `grpcadapter` and corresponding `grpcpb` (though gRPC primarily uses Protobuf).

## II. Observability and Monitoring (Improving Insights)

*   **OpenTelemetry Integration:**
    *   The project already has OpenTelemetry dependencies (`go.opentelemetry.io/otel`).
    *   **Goal:** Ensure comprehensive tracing and metrics are emitted for all critical paths in the RPC flow (client call, server request handling, adapter interactions, encoding/decoding, compression).
    *   Review existing telemetry in `telemetry/telemetry.go` and expand its coverage.
    *   Ensure trace context propagation across client and server.
    *   Provide clear examples of how to configure and use different OpenTelemetry exporters (OTLP, Prometheus, Jaeger, etc.).
    *   Update documentation with OpenTelemetry setup and usage.
*   **(New) Enhanced Metrics:**
    *   Beyond basic call counts/latency, consider adding metrics for:
        *   Payload sizes (before/after compression).
        *   Error rates categorized by error type (client, server, network).
        *   Queue lengths or processing times if applicable (e.g., in worker pools).
        *   Connection status for adapters.

## III. Encoding and Data Handling (Flexibility and Efficiency)

*   **(New) Support for More Encoding Formats:**
    *   **MessagePack:** A binary serialization format that is fast and compact.
    *   **Avro:** Another popular binary serialization format, especially in Kafka ecosystems.
    *   This would involve creating new packages like `msgpack` and `avro` under `mqttpb`, `mqttjson` (or more generic naming if other transports are added).
*   **(New) Schema Registry Integration (Optional but Advanced):**
    *   For Protobuf/Avro, consider optional integration with a schema registry (e.g., Confluent Schema Registry) to manage schema evolution.
*   **Review Compression:**
    *   The `compressor` package exists. Ensure it's being used effectively and benchmark different compression algorithms (Gzip, Brotli, Snappy, Zstd) for typical payloads.
    *   Allow configurable compression levels.

## IV. Core Framework Improvements (Robustness and Usability)

*   **(New) Pluggable Authentication/Authorization:**
    *   Currently, authentication seems tied to the underlying transport (e.g., MQTT username/password).
    *   Consider adding hooks or middleware points for custom auth logic at the RPC layer.
*   **(New) Server-Side Streaming RPCs:**
    *   The current model appears to be request-response. Explore adding support for server-side streaming where the server can send multiple messages back to the client for a single request.
*   **(New) Client-Side Load Balancing / Failover:**
    *   If a client can connect to multiple server instances (perhaps via a broker), implement strategies for load balancing or failing over to another instance.
*   **(New) Improved Error Handling and Propagation:**
    *   Review how errors are handled and propagated between client, server, and adapters. Ensure errors are typed and carry sufficient context. The `cockroachdb/errors` library is already in use, which is good.
*   **(New) Configuration Standardization:**
    *   Review options (`server_option.go`, `mqttadapter/options.go`). Aim for a consistent and clear way to configure clients and servers.
    *   Consider using a library for struct-based configuration with environment variable overrides (e.g., Viper).
*   **(New) Dead Letter Queue (DLQ) for Failed Requests:**
    *   For requests that cannot be processed after retries (if any), provide an option to send them to a DLQ for later inspection. This is particularly relevant for asynchronous processing.

## V. Developer Experience and Documentation

*   **(New) API Documentation Review:**
    *   Use GoDoc and ensure all public APIs are well-commented.
    *   Generate and publish documentation (e.g., using `pkg.go.dev`).
*   **(New) More Examples:**
    *   Add examples showcasing specific features:
        *   Different encoding formats.
        *   OpenTelemetry setup with a common backend (e.g., Jaeger/Prometheus).
        *   Advanced error handling.
        *   Usage with new protocols (WebSocket, AMQP) once added.
*   **(New) Contribution Guidelines:**
    *   `CONTRIBUTING.md` is good. Ensure it's kept up to date, especially as the project grows.
*   **(New) Benchmarking Suite:**
    *   Expand the load testing example into a more formal benchmarking suite.
    *   Regularly run benchmarks to track performance regressions or improvements.
*   **(New) Code Coverage:**
    *   Aim for high code coverage and integrate with a service like Codecov.

## VI. Project Management and Maintenance

*   **(New) Issue Tracking and Milestones:**
    *   Use GitHub Issues effectively. Label issues (bug, feature, documentation) and group them into milestones for releases.
*   **(New) Dependency Management:**
    *   Regularly review and update dependencies (`go mod tidy`, `go get -u ./...`).
    *   Monitor for security vulnerabilities in dependencies.
*   **(New) Release Strategy:**
    *   Define a clear release strategy (e.g., semantic versioning).
    *   Automate release processes using GitHub Actions.
