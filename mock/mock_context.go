// Code generated by MockGen. DO NOT EDIT.
// Source: context.go
//
// Generated by this command:
//
//	mockgen -source=context.go -destination=mock/mock_context.go
//

// Package mock_reverserpc is a generated GoMock package.
package mock_reverserpc

import (
	context "context"
	reflect "reflect"

	prometheus "github.com/prometheus/client_golang/prometheus"
	reverserpc "github.com/xizhibei/go-reverse-rpc"
	gomock "go.uber.org/mock/gomock"
)

// MockChildContext is a mock of ChildContext interface.
type MockChildContext struct {
	ctrl     *gomock.Controller
	recorder *MockChildContextMockRecorder
}

// MockChildContextMockRecorder is the mock recorder for MockChildContext.
type MockChildContextMockRecorder struct {
	mock *MockChildContext
}

// NewMockChildContext creates a new mock instance.
func NewMockChildContext(ctrl *gomock.Controller) *MockChildContext {
	mock := &MockChildContext{ctrl: ctrl}
	mock.recorder = &MockChildContextMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChildContext) EXPECT() *MockChildContextMockRecorder {
	return m.recorder
}

// Bind mocks base method.
func (m *MockChildContext) Bind(request any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bind", request)
	ret0, _ := ret[0].(error)
	return ret0
}

// Bind indicates an expected call of Bind.
func (mr *MockChildContextMockRecorder) Bind(request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bind", reflect.TypeOf((*MockChildContext)(nil).Bind), request)
}

// ID mocks base method.
func (m *MockChildContext) ID() *reverserpc.ID {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ID")
	ret0, _ := ret[0].(*reverserpc.ID)
	return ret0
}

// ID indicates an expected call of ID.
func (mr *MockChildContextMockRecorder) ID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ID", reflect.TypeOf((*MockChildContext)(nil).ID))
}

// Method mocks base method.
func (m *MockChildContext) Method() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Method")
	ret0, _ := ret[0].(string)
	return ret0
}

// Method indicates an expected call of Method.
func (mr *MockChildContextMockRecorder) Method() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Method", reflect.TypeOf((*MockChildContext)(nil).Method))
}

// PrometheusLabels mocks base method.
func (m *MockChildContext) PrometheusLabels() prometheus.Labels {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrometheusLabels")
	ret0, _ := ret[0].(prometheus.Labels)
	return ret0
}

// PrometheusLabels indicates an expected call of PrometheusLabels.
func (mr *MockChildContextMockRecorder) PrometheusLabels() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrometheusLabels", reflect.TypeOf((*MockChildContext)(nil).PrometheusLabels))
}

// Reply mocks base method.
func (m *MockChildContext) Reply(res *reverserpc.Response) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reply", res)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Reply indicates an expected call of Reply.
func (mr *MockChildContextMockRecorder) Reply(res any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reply", reflect.TypeOf((*MockChildContext)(nil).Reply), res)
}

// ReplyDesc mocks base method.
func (m *MockChildContext) ReplyDesc() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplyDesc")
	ret0, _ := ret[0].(string)
	return ret0
}

// ReplyDesc indicates an expected call of ReplyDesc.
func (mr *MockChildContextMockRecorder) ReplyDesc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplyDesc", reflect.TypeOf((*MockChildContext)(nil).ReplyDesc))
}

// MockContext is a mock of Context interface.
type MockContext struct {
	ctrl     *gomock.Controller
	recorder *MockContextMockRecorder
}

// MockContextMockRecorder is the mock recorder for MockContext.
type MockContextMockRecorder struct {
	mock *MockContext
}

// NewMockContext creates a new mock instance.
func NewMockContext(ctrl *gomock.Controller) *MockContext {
	mock := &MockContext{ctrl: ctrl}
	mock.recorder = &MockContextMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContext) EXPECT() *MockContextMockRecorder {
	return m.recorder
}

// Bind mocks base method.
func (m *MockContext) Bind(request any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bind", request)
	ret0, _ := ret[0].(error)
	return ret0
}

// Bind indicates an expected call of Bind.
func (mr *MockContextMockRecorder) Bind(request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bind", reflect.TypeOf((*MockContext)(nil).Bind), request)
}

// Ctx mocks base method.
func (m *MockContext) Ctx() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ctx")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Ctx indicates an expected call of Ctx.
func (mr *MockContextMockRecorder) Ctx() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ctx", reflect.TypeOf((*MockContext)(nil).Ctx))
}

// GetResponse mocks base method.
func (m *MockContext) GetResponse() *reverserpc.Response {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResponse")
	ret0, _ := ret[0].(*reverserpc.Response)
	return ret0
}

// GetResponse indicates an expected call of GetResponse.
func (mr *MockContextMockRecorder) GetResponse() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResponse", reflect.TypeOf((*MockContext)(nil).GetResponse))
}

// ID mocks base method.
func (m *MockContext) ID() *reverserpc.ID {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ID")
	ret0, _ := ret[0].(*reverserpc.ID)
	return ret0
}

// ID indicates an expected call of ID.
func (mr *MockContextMockRecorder) ID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ID", reflect.TypeOf((*MockContext)(nil).ID))
}

// Method mocks base method.
func (m *MockContext) Method() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Method")
	ret0, _ := ret[0].(string)
	return ret0
}

// Method indicates an expected call of Method.
func (mr *MockContextMockRecorder) Method() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Method", reflect.TypeOf((*MockContext)(nil).Method))
}

// PrometheusLabels mocks base method.
func (m *MockContext) PrometheusLabels() prometheus.Labels {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrometheusLabels")
	ret0, _ := ret[0].(prometheus.Labels)
	return ret0
}

// PrometheusLabels indicates an expected call of PrometheusLabels.
func (mr *MockContextMockRecorder) PrometheusLabels() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrometheusLabels", reflect.TypeOf((*MockContext)(nil).PrometheusLabels))
}

// Reply mocks base method.
func (m *MockContext) Reply(res *reverserpc.Response) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reply", res)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Reply indicates an expected call of Reply.
func (mr *MockContextMockRecorder) Reply(res any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reply", reflect.TypeOf((*MockContext)(nil).Reply), res)
}

// ReplyDesc mocks base method.
func (m *MockContext) ReplyDesc() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplyDesc")
	ret0, _ := ret[0].(string)
	return ret0
}

// ReplyDesc indicates an expected call of ReplyDesc.
func (mr *MockContextMockRecorder) ReplyDesc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplyDesc", reflect.TypeOf((*MockContext)(nil).ReplyDesc))
}

// ReplyError mocks base method.
func (m *MockContext) ReplyError(status int, err error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplyError", status, err)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ReplyError indicates an expected call of ReplyError.
func (mr *MockContextMockRecorder) ReplyError(status, err any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplyError", reflect.TypeOf((*MockContext)(nil).ReplyError), status, err)
}

// ReplyOK mocks base method.
func (m *MockContext) ReplyOK(data any) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplyOK", data)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ReplyOK indicates an expected call of ReplyOK.
func (mr *MockContextMockRecorder) ReplyOK(data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplyOK", reflect.TypeOf((*MockContext)(nil).ReplyOK), data)
}
